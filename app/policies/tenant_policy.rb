# frozen_string_literal: true

class TenantPolicy < ApplicationPolicy
  def index?
    admin?
  end

  def show?
    admin? || tenant_member?
  end

  def create?
    admin?
  end

  def update?
    admin? || tenant_admin?
  end

  def destroy?
    admin? && !current_tenant_destruction?
  end

  def activate?
    admin?
  end

  def deactivate?
    admin? && !current_tenant_deactivation?
  end

  def switch_to?
    admin? || tenant_member?
  end

  protected

  def tenant_member?
    user&.tenant_id == record.id
  end

  def tenant_admin?
    tenant_member? && admin?
  end

  def current_tenant_destruction?
    ActsAsTenant.current_tenant&.id == record.id
  end

  def current_tenant_deactivation?
    ActsAsTenant.current_tenant&.id == record.id
  end

  # Override same_tenant? for tenant policy since tenants don't belong to tenants
  def same_tenant?
    true
  end

  class Scope < ApplicationPolicy::Scope
    def resolve
      if user&.role == "admin"
        # Admins can see all tenants
        scope.all
      else
        # Regular users can only see their own tenant
        scope.where(id: user.tenant_id)
      end
    end
  end
end
