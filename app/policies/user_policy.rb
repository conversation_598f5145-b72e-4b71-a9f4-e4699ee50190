# frozen_string_literal: true

class UserPolicy < ApplicationPolicy
  def index?
    admin?
  end

  def show?
    admin? || owner?
  end

  def create?
    admin?
  end

  def update?
    admin? || owner?
  end

  def destroy?
    admin? && !self_destruction?
  end

  def change_role?
    admin? && !self_role_change?
  end

  def activate?
    admin?
  end

  def deactivate?
    admin? && !self_deactivation?
  end

  protected

  def owner?
    record.id == user&.id
  end

  def self_destruction?
    record.id == user&.id
  end

  def self_role_change?
    record.id == user&.id
  end

  def self_deactivation?
    record.id == user&.id
  end

  class Scope < ApplicationPolicy::Scope
    def resolve
      if user&.role == "admin"
        # Admins can see all users in their tenant
        super
      else
        # Regular users can only see themselves
        scope.where(id: user.id)
      end
    end
  end
end
