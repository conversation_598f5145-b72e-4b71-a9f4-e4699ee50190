# frozen_string_literal: true

class ApplicationPolicy
  attr_reader :user, :record

  def initialize(user, record)
    @user = user
    @record = record
  end

  def index?
    user_authenticated? && same_tenant?
  end

  def show?
    user_authenticated? && same_tenant?
  end

  def create?
    user_authenticated? && same_tenant?
  end

  def new?
    create?
  end

  def update?
    user_authenticated? && same_tenant? && (owner? || admin?)
  end

  def edit?
    update?
  end

  def destroy?
    user_authenticated? && same_tenant? && (owner? || admin?)
  end

  protected

  # Check if user is authenticated
  def user_authenticated?
    user.present?
  end

  # Check if user is admin (assuming role-based authorization)
  def admin?
    user&.role == "admin"
  end

  # Check if user owns the record (override in specific policies)
  def owner?
    return false unless record.respond_to?(:user_id)
    record.user_id == user&.id
  end

  # Check if record belongs to the same tenant as the user
  def same_tenant?
    return true unless tenant_aware?
    return true unless user&.respond_to?(:tenant_id)
    return true unless record&.respond_to?(:tenant_id)

    user.tenant_id == record.tenant_id
  end

  # Check if the model is tenant-aware
  def tenant_aware?
    record.class.respond_to?(:acts_as_tenant?) && record.class.acts_as_tenant?
  end

  class Scope
    def initialize(user, scope)
      @user = user
      @scope = scope
    end

    def resolve
      return scope.none unless user

      if tenant_aware?
        scope.where(tenant_id: user.tenant_id)
      else
        scope.all
      end
    end

    protected

    attr_reader :user, :scope

    # Check if the model is tenant-aware
    def tenant_aware?
      scope.respond_to?(:acts_as_tenant?) && scope.acts_as_tenant?
    end
  end
end
