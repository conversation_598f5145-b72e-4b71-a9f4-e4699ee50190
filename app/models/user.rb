class User < ApplicationRecord
  # Acts As Tenant configuration - only for users with tenants
  acts_as_tenant :tenant, optional: true

  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable

  # Associations
  belongs_to :tenant, optional: true

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :role, inclusion: { in: %w[admin user manager], message: "%{value} is not a valid role" }

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }
  scope :admins, -> { where(role: "admin") }
  scope :managers, -> { where(role: "manager") }
  scope :regular_users, -> { where(role: "user") }

  # Callbacks
  before_validation :set_default_values, on: :create

  # Instance methods
  def admin?
    role == "admin"
  end

  def manager?
    role == "manager"
  end

  def regular_user?
    role == "user"
  end

  def active?
    active
  end

  def inactive?
    !active
  end

  def full_name
    "#{first_name} #{last_name}".strip
  end

  def display_name
    full_name.present? ? full_name : name
  end

  def deactivate!
    update!(active: false)
  end

  def activate!
    update!(active: true)
  end

  private

  def set_default_values
    self.role ||= "user"
    self.active = true if active.nil?
  end
end
