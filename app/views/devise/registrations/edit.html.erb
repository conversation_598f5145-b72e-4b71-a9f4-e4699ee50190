<div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-2xl mx-auto">
    <div class="bg-white shadow rounded-lg">
      <!-- Header -->
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-2xl font-bold text-gray-900">Account Settings</h2>
        <p class="mt-1 text-sm text-gray-600">Manage your account information and security settings.</p>
      </div>

      <!-- Form -->
      <div class="px-6 py-6">
        <%= form_for(resource, as: resource_name, url: registration_path(resource_name),
                      html: { method: :put, class: "space-y-6" }) do |f| %>

          <!-- Error Messages -->
          <%= render "devise/shared/error_messages", resource: resource %>

          <!-- Email Confirmation Notice -->
          <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
            <div class="rounded-md bg-yellow-50 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-yellow-800">
                    Currently waiting confirmation for: <strong><%= resource.unconfirmed_email %></strong>
                  </p>
                </div>
              </div>
            </div>
          <% end %>

          <div class="grid grid-cols-1 gap-6">
            <!-- Name Field -->
            <div>
              <%= f.label :name, "Full Name", class: "block text-sm font-medium text-gray-700" %>
              <div class="mt-1">
                <%= f.text_field :name,
                      class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                      placeholder: "Enter your full name" %>
              </div>
            </div>

            <!-- Email Field -->
            <div>
              <%= f.label :email, "Email Address", class: "block text-sm font-medium text-gray-700" %>
              <div class="mt-1">
                <%= f.email_field :email,
                      autofocus: true,
                      autocomplete: "email",
                      class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                      placeholder: "Enter your email address" %>
              </div>
            </div>

            <!-- Password Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Change Password</h3>
              <div class="space-y-4">
                <!-- New Password -->
                <div>
                  <%= f.label :password, class: "block text-sm font-medium text-gray-700" do %>
                    New Password
                    <span class="text-gray-500 text-xs">(leave blank if you don't want to change it)</span>
                    <% if @minimum_password_length %>
                      <span class="text-gray-500 text-xs">(<%= @minimum_password_length %> characters minimum)</span>
                    <% end %>
                  <% end %>
                  <div class="mt-1">
                    <%= f.password_field :password,
                          autocomplete: "new-password",
                          class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                          placeholder: "Enter new password (optional)" %>
                  </div>
                </div>

                <!-- Confirm Password -->
                <div>
                  <%= f.label :password_confirmation, "Confirm New Password", class: "block text-sm font-medium text-gray-700" %>
                  <div class="mt-1">
                    <%= f.password_field :password_confirmation,
                          autocomplete: "new-password",
                          class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                          placeholder: "Confirm new password" %>
                  </div>
                </div>

                <!-- Current Password -->
                <div>
                  <%= f.label :current_password, class: "block text-sm font-medium text-gray-700" do %>
                    Current Password
                    <span class="text-gray-500 text-xs">(required to confirm changes)</span>
                  <% end %>
                  <div class="mt-1">
                    <%= f.password_field :current_password,
                          autocomplete: "current-password",
                          required: true,
                          class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                          placeholder: "Enter your current password" %>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-between items-center pt-6 border-t border-gray-200">
            <div class="flex space-x-3">
              <%= f.submit "Update Account",
                    class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out" %>
              <%= link_to "Cancel", :back,
                    class: "inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out" %>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Danger Zone -->
      <div class="px-6 py-4 bg-red-50 border-t border-red-200">
        <h3 class="text-lg font-medium text-red-900 mb-2">Danger Zone</h3>
        <p class="text-sm text-red-700 mb-4">
          Once you delete your account, there is no going back. Please be certain.
        </p>
        <%= button_to "Delete Account", registration_path(resource_name),
              method: :delete,
              data: {
                "turbo-confirm": "Are you sure you want to delete your account? This action cannot be undone."
              },
              class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out" %>
      </div>
    </div>
  </div>
</div>
