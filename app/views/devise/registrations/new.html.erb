<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- <PERSON><PERSON> and Header -->
    <div class="text-center">
        <div class="flex justify-center">
            <div class="w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center mb-4">
                <i class="fas fa-chart-line text-white text-2xl"></i>
            </div>
        </div>
        <h2 class="text-3xl font-bold text-gray-900">Create your account</h2>
        <p class="mt-2 text-gray-600">Start your 14-day free trial today</p>
    </div>
    <!-- Signup Form -->
    <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { class: "space-y-6" }) do |f| %>
      <%= render "devise/shared/error_messages", resource: resource %>
      <%= f.hidden_field :plan_id, value: params[:plan_id] %>
          
        <div class="bg-white rounded-2xl shadow-xl p-8">
            <form class="space-y-6">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="first-name" class="block text-sm font-medium text-gray-700 mb-2">
                            First name
                        </label>
                        <input id="first-name" name="first-name" type="text" required 
                                class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                                placeholder="John">
                    </div>
                    <div>
                        <label for="last-name" class="block text-sm font-medium text-gray-700 mb-2">
                            Last name
                        </label>
                        <input id="last-name" name="last-name" type="text" required 
                                class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                                placeholder="Doe">
                    </div>
                </div>

                <div>
                    <label for="company" class="block text-sm font-medium text-gray-700 mb-2">
                        Company name
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-building text-gray-400"></i>
                        </div>
                        <input id="company" name="company" type="text" required 
                                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                                placeholder="Your company name">
                    </div>
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        Email address
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        <input id="email" name="email" type="email" required 
                                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                                placeholder="<EMAIL>">
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        Password
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input id="password" name="password" type="password" required 
                                class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                                placeholder="Create a strong password">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="flex space-x-1">
                            <div class="h-1 w-1/4 bg-red-300 rounded"></div>
                            <div class="h-1 w-1/4 bg-gray-200 rounded"></div>
                            <div class="h-1 w-1/4 bg-gray-200 rounded"></div>
                            <div class="h-1 w-1/4 bg-gray-200 rounded"></div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Password strength: Weak</p>
                    </div>
                </div>

                <div>
                    <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-2">
                        Confirm password
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input id="confirm-password" name="confirm-password" type="password" required 
                                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                                placeholder="Confirm your password">
                    </div>
                </div>

                <div>
                    <label for="business-size" class="block text-sm font-medium text-gray-700 mb-2">
                        Business size
                    </label>
                    <select id="business-size" name="business-size" required 
                            class="block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors">
                        <option value="">Select your business size</option>
                        <option value="1-10">1-10 employees</option>
                        <option value="11-50">11-50 employees</option>
                        <option value="51-200">51-200 employees</option>
                        <option value="201-500">201-500 employees</option>
                        <option value="500+">500+ employees</option>
                    </select>
                </div>

                <div class="flex items-start">
                    <div class="flex items-center h-5">
                        <input id="terms" name="terms" type="checkbox" required
                                class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="terms" class="text-gray-700">
                            I agree to the 
                            <a href="#" class="text-primary hover:text-blue-700 font-medium">Terms of Service</a> 
                            and 
                            <a href="#" class="text-primary hover:text-blue-700 font-medium">Privacy Policy</a>
                        </label>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex items-center h-5">
                        <input id="marketing" name="marketing" type="checkbox"
                                class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="marketing" class="text-gray-700">
                            I'd like to receive marketing emails about new features and tips
                        </label>
                    </div>
                </div>

                <div>
                    <button type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-user-plus text-blue-300 group-hover:text-blue-200"></i>
                        </span>
                        Start your free trial
                    </button>
                </div>

                <!-- Social Signup -->
                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Or sign up with</span>
                        </div>
                    </div>

                    <div class="mt-6 grid grid-cols-2 gap-3">
                        <button type="button" 
                                class="w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors">
                            <i class="fab fa-google text-red-500"></i>
                            <span class="ml-2">Google</span>
                        </button>
                        <button type="button" 
                                class="w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors">
                            <i class="fab fa-microsoft text-blue-500"></i>
                            <span class="ml-2">Microsoft</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Login link -->
        <div class="text-center">
            <p class="text-sm text-gray-600">
                Already have an account?
                <a href="login.html" class="font-medium text-primary hover:text-blue-700 transition-colors">
                    Sign in
                </a>
            </p>
        </div>

        <!-- Trial Benefits -->
        <div class="bg-white rounded-xl p-6 shadow-lg">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 text-center">Your 14-day trial includes:</h3>
            <div class="space-y-3">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-envelope text-accent text-sm"></i>
                    </div>
                    <span class="text-sm text-gray-700">Unlimited email campaigns</span>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-primary text-sm"></i>
                    </div>
                    <span class="text-sm text-gray-700">Up to 10,000 contacts</span>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-robot text-secondary text-sm"></i>
                    </div>
                    <span class="text-sm text-gray-700">AI-powered automation</span>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-yellow-600 text-sm"></i>
                    </div>
                    <span class="text-sm text-gray-700">Advanced analytics</span>
                </div>
            </div>
        </div>
    </div>
    <% end %>
  </div>
</div>
