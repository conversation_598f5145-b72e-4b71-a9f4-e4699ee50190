<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
        </svg>
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Create your account
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Join us today! Fill in the details below to get started.
      </p>
    </div>

    <%= form_for(resource, as: resource_name, url: registration_path(resource_name),
                  html: { class: "mt-8 space-y-6" }) do |f| %>

      <!-- Error Messages -->
      <% if resource.errors.any? %>
        <div class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                Please correct the following errors:
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc pl-5 space-y-1">
                  <% resource.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <div class="space-y-4">
        <!-- Name Field -->
        <div>
          <%= f.label :name, "Full Name", class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.text_field :name,
                  autofocus: true,
                  required: true,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                  placeholder: "Enter your full name" %>
          </div>
        </div>

        <!-- Email Field -->
        <div>
          <%= f.label :email, "Email Address", class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.email_field :email,
                  autocomplete: "email",
                  required: true,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                  placeholder: "Enter your email address" %>
          </div>
        </div>

        <!-- Password Field -->
        <div>
          <%= f.label :password, class: "block text-sm font-medium text-gray-700" do %>
            Password
            <% if @minimum_password_length %>
              <span class="text-gray-500 text-xs">(<%= @minimum_password_length %> characters minimum)</span>
            <% end %>
          <% end %>
          <div class="mt-1">
            <%= f.password_field :password,
                  autocomplete: "new-password",
                  required: true,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                  placeholder: "Create a secure password" %>
          </div>
        </div>

        <!-- Password Confirmation Field -->
        <div>
          <%= f.label :password_confirmation, "Confirm Password", class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.password_field :password_confirmation,
                  autocomplete: "new-password",
                  required: true,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                  placeholder: "Confirm your password" %>
          </div>
        </div>
      </div>

      <div>
        <%= f.submit "Create Account",
              class: "group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out" %>
      </div>

      <div class="text-center">
        <span class="text-sm text-gray-600">Already have an account?</span>
        <%= link_to "Sign in here", new_session_path(resource_name),
              class: "font-medium text-blue-600 hover:text-blue-500 ml-1" %>
      </div>
    <% end %>
  </div>
</div>
