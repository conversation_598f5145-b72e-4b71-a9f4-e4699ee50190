<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-yellow-100">
        <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
        </svg>
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Forgot your password?
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        No worries! Enter your email address and we'll send you instructions to reset your password.
      </p>
    </div>

    <%= form_for(resource, as: resource_name, url: password_path(resource_name),
                  html: { method: :post, class: "mt-8 space-y-6" }) do |f| %>

      <!-- Error Messages -->
      <%= render "devise/shared/error_messages", resource: resource %>

      <div>
        <%= f.label :email, "Email Address", class: "block text-sm font-medium text-gray-700" %>
        <div class="mt-1">
          <%= f.email_field :email,
                autofocus: true,
                autocomplete: "email",
                required: true,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                placeholder: "Enter your email address" %>
        </div>
      </div>

      <div>
        <%= f.submit "Send Reset Instructions",
              class: "group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition duration-150 ease-in-out" %>
      </div>

      <div class="text-center">
        <span class="text-sm text-gray-600">Remember your password?</span>
        <%= link_to "Sign in here", new_session_path(resource_name),
              class: "font-medium text-blue-600 hover:text-blue-500 ml-1" %>
      </div>
    <% end %>
  </div>
</div>
