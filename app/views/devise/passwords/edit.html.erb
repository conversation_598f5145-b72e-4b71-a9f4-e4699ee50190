<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100">
        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
        </svg>
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Reset your password
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Enter your new password below to complete the reset process.
      </p>
    </div>

    <%= form_for(resource, as: resource_name, url: password_path(resource_name),
                  html: { method: :put, class: "mt-8 space-y-6" }) do |f| %>

      <!-- Error Messages -->
      <%= render "devise/shared/error_messages", resource: resource %>
      <%= f.hidden_field :reset_password_token %>

      <div class="space-y-4">
        <!-- New Password Field -->
        <div>
          <%= f.label :password, class: "block text-sm font-medium text-gray-700" do %>
            New Password
            <% if @minimum_password_length %>
              <span class="text-gray-500 text-xs">(<%= @minimum_password_length %> characters minimum)</span>
            <% end %>
          <% end %>
          <div class="mt-1">
            <%= f.password_field :password,
                  autofocus: true,
                  autocomplete: "new-password",
                  required: true,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                  placeholder: "Enter your new password" %>
          </div>
        </div>

        <!-- Confirm Password Field -->
        <div>
          <%= f.label :password_confirmation, "Confirm New Password", class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.password_field :password_confirmation,
                  autocomplete: "new-password",
                  required: true,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                  placeholder: "Confirm your new password" %>
          </div>
        </div>
      </div>

      <div>
        <%= f.submit "Update Password",
              class: "group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out" %>
      </div>

      <div class="text-center">
        <%= link_to "Back to Sign In", new_session_path(resource_name),
              class: "font-medium text-blue-600 hover:text-blue-500" %>
      </div>
    <% end %>
  </div>
</div>
