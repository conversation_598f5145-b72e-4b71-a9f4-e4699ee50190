<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Marketing Ai" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body>
    <nav class="bg-gray-800 text-white p-4">
      <div class="container mx-auto flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <%= link_to "Marketing AI", root_path, class: "text-xl font-bold" %>
          <% if user_signed_in? %>
            <%= link_to "Users", users_path, class: "hover:text-gray-300" %>
            <% if policy(Tenant).index? %>
              <%= link_to "Tenants", tenants_path, class: "hover:text-gray-300" %>
            <% end %>
          <% end %>
        </div>

        <div class="flex items-center space-x-4">
          <% if user_signed_in? %>
            <span class="text-sm">
              Welcome, <%= current_user.display_name %>
              <% if current_tenant %>
                (<%= current_tenant.name %>)
              <% else %>
                (Super Admin)
              <% end %>
            </span>
            <%= form_with url: destroy_user_session_path, method: :delete, local: true, style: "display: inline;" do |form| %>
              <%= form.submit "Sign Out", class: "hover:text-gray-300 bg-transparent border-none text-white cursor-pointer" %>
            <% end %>
          <% else %>
            <%= link_to "Sign In", new_user_session_path, class: "hover:text-gray-300" %>
            <%= link_to "Sign Up", new_user_registration_path, class: "hover:text-gray-300" %>
          <% end %>
        </div>
      </div>
    </nav>

    <% if notice %>
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mx-4 mt-4">
        <%= notice %>
      </div>
    <% end %>

    <% if alert %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mx-4 mt-4">
        <%= alert %>
      </div>
    <% end %>

    <main class="container mx-auto px-4 py-8">
      <%= yield %>
    </main>
  </body>
</html>
