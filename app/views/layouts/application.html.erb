<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Marketing Ai" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gray-50 min-h-screen flex flex-col">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <!-- Left side - Logo and main navigation -->
          <div class="flex items-center">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
              <%= link_to root_path, class: "flex items-center space-x-2" do %>
                <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <span class="text-xl font-bold text-gray-900">Marketing AI</span>
              <% end %>
            </div>

            <!-- Main Navigation (Desktop) -->
            <% if user_signed_in? %>
              <div class="hidden md:ml-8 md:flex md:space-x-8">
                <%= link_to "Dashboard", root_path,
                      class: "text-gray-900 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out" %>

                <% if policy(User).index? %>
                  <%= link_to "Users", users_path,
                        class: "text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out" %>
                <% end %>

                <% if policy(Tenant).index? %>
                  <%= link_to "Tenants", tenants_path,
                        class: "text-gray-500 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out" %>
                <% end %>
              </div>
            <% end %>
          </div>

          <!-- Right side - User menu and actions -->
          <div class="flex items-center space-x-4">
            <% if user_signed_in? %>
              <!-- Tenant Info (if applicable) -->
              <% if current_tenant %>
                <div class="hidden md:flex items-center space-x-2 px-3 py-1 bg-blue-50 rounded-full">
                  <div class="h-2 w-2 bg-blue-500 rounded-full"></div>
                  <span class="text-xs font-medium text-blue-700"><%= current_tenant.name %></span>
                </div>
              <% elsif current_user.admin? %>
                <div class="hidden md:flex items-center space-x-2 px-3 py-1 bg-purple-50 rounded-full">
                  <div class="h-2 w-2 bg-purple-500 rounded-full"></div>
                  <span class="text-xs font-medium text-purple-700">Super Admin</span>
                </div>
              <% end %>

              <!-- User Dropdown -->
              <div class="relative" data-controller="dropdown">
                <button data-action="click->dropdown#toggle"
                        class="flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md p-2">
                  <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-gray-700">
                      <%= current_user.display_name.first.upcase %>
                    </span>
                  </div>
                  <span class="hidden md:block text-sm font-medium"><%= current_user.display_name %></span>
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>

                <!-- Dropdown Menu -->
                <div data-dropdown-target="menu"
                     class="hidden absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                  <div class="py-1">
                    <!-- User Info -->
                    <div class="px-4 py-2 border-b border-gray-100">
                      <p class="text-sm font-medium text-gray-900"><%= current_user.display_name %></p>
                      <p class="text-sm text-gray-500"><%= current_user.email %></p>
                      <p class="text-xs text-gray-400 mt-1">
                        Role: <%= current_user.role.capitalize %>
                        <% if current_tenant %>
                          • <%= current_tenant.name %>
                        <% end %>
                      </p>
                    </div>

                    <!-- Menu Items -->
                    <%= link_to edit_user_registration_path,
                          class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                      <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                      </svg>
                      Account Settings
                    <% end %>

                    <% if policy(current_user).show? %>
                      <%= link_to user_path(current_user),
                            class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                        <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        View Profile
                      <% end %>
                    <% end %>

                    <div class="border-t border-gray-100"></div>

                    <%= link_to "/users/sign_out",
                          class: "flex items-center px-4 py-2 text-sm text-red-700 hover:bg-red-50" do %>
                      <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                      </svg>
                      Sign Out
                    <% end %>
                  </div>
                </div>
              </div>
            <% else %>
              <!-- Guest Navigation -->
              <div class="flex items-center space-x-4">
                <%= link_to "Sign In", new_user_session_path,
                      class: "text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out" %>
                <%= link_to "Sign Up", new_user_registration_path,
                      class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-150 ease-in-out" %>
              </div>
            <% end %>

            <!-- Mobile menu button -->
            <div class="md:hidden">
              <button data-controller="mobile-menu" data-action="click->mobile-menu#toggle"
                      class="text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 p-2">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <% if user_signed_in? %>
          <div data-mobile-menu-target="menu" class="hidden md:hidden border-t border-gray-200 pt-4 pb-3">
            <div class="space-y-1">
              <%= link_to "Dashboard", root_path,
                    class: "block px-3 py-2 text-base font-medium text-gray-900 hover:text-blue-600 hover:bg-gray-50 rounded-md" %>

              <% if policy(User).index? %>
                <%= link_to "Users", users_path,
                      class: "block px-3 py-2 text-base font-medium text-gray-500 hover:text-blue-600 hover:bg-gray-50 rounded-md" %>
              <% end %>

              <% if policy(Tenant).index? %>
                <%= link_to "Tenants", tenants_path,
                      class: "block px-3 py-2 text-base font-medium text-gray-500 hover:text-blue-600 hover:bg-gray-50 rounded-md" %>
              <% end %>
            </div>

            <div class="border-t border-gray-200 pt-4 mt-4">
              <div class="px-3 py-2">
                <p class="text-base font-medium text-gray-900"><%= current_user.display_name %></p>
                <p class="text-sm text-gray-500"><%= current_user.email %></p>
              </div>
              <div class="space-y-1 mt-3">
                <%= link_to "Account Settings", edit_user_registration_path,
                      class: "block px-3 py-2 text-base font-medium text-gray-500 hover:text-gray-900 hover:bg-gray-50 rounded-md" %>
                <%= link_to "Sign Out", "/users/sign_out",
                      class: "block px-3 py-2 text-base font-medium text-red-600 hover:text-red-900 hover:bg-red-50 rounded-md" %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </nav>

    <% if notice %>
      <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mx-4 mt-4">
        <%= notice %>
      </div>
    <% end %>

    <% if alert %>
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mx-4 mt-4">
        <%= alert %>
      </div>
    <% end %>

    <!-- Main Content -->
    <main class="flex-1">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <%= yield %>
      </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-50 border-t border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Company Info -->
          <div class="col-span-1 md:col-span-2">
            <div class="flex items-center space-x-2 mb-4">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <span class="text-xl font-bold text-gray-900">Marketing AI</span>
            </div>
            <p class="text-gray-600 mb-4 max-w-md">
              Empowering businesses with intelligent marketing solutions.
              Streamline your marketing operations with our comprehensive platform.
            </p>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-400 hover:text-gray-600 transition duration-150 ease-in-out">
                <span class="sr-only">Twitter</span>
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"></path>
                </svg>
              </a>
              <a href="#" class="text-gray-400 hover:text-gray-600 transition duration-150 ease-in-out">
                <span class="sr-only">LinkedIn</span>
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"></path>
                </svg>
              </a>
              <a href="#" class="text-gray-400 hover:text-gray-600 transition duration-150 ease-in-out">
                <span class="sr-only">GitHub</span>
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd"></path>
                </svg>
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">Platform</h3>
            <ul class="space-y-3">
              <% if user_signed_in? %>
                <li><%= link_to "Dashboard", root_path, class: "text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out" %></li>
                <% if policy(User).index? %>
                  <li><%= link_to "Users", users_path, class: "text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out" %></li>
                <% end %>
                <% if policy(Tenant).index? %>
                  <li><%= link_to "Tenants", tenants_path, class: "text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out" %></li>
                <% end %>
                <li><%= link_to "Account Settings", edit_user_registration_path, class: "text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out" %></li>
              <% else %>
                <li><%= link_to "Sign In", new_user_session_path, class: "text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out" %></li>
                <li><%= link_to "Sign Up", new_user_registration_path, class: "text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out" %></li>
              <% end %>
            </ul>
          </div>

          <!-- Support -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">Support</h3>
            <ul class="space-y-3">
              <li><a href="#" class="text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out">Help Center</a></li>
              <li><a href="#" class="text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out">Documentation</a></li>
              <li><a href="#" class="text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out">Contact Us</a></li>
              <li><a href="#" class="text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out">Status Page</a></li>
            </ul>
          </div>
        </div>

        <!-- Bottom Section -->
        <div class="border-t border-gray-200 pt-8 mt-8">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <div class="flex items-center space-x-6 mb-4 md:mb-0">
              <a href="#" class="text-sm text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out">Privacy Policy</a>
              <a href="#" class="text-sm text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out">Terms of Service</a>
              <a href="#" class="text-sm text-gray-600 hover:text-gray-900 transition duration-150 ease-in-out">Cookie Policy</a>
            </div>
            <div class="flex items-center space-x-4">
              <p class="text-sm text-gray-600">
                © <%= Date.current.year %> Marketing AI. All rights reserved.
              </p>
              <% if user_signed_in? && current_tenant %>
                <span class="text-xs text-gray-400">
                  Tenant: <%= current_tenant.subdomain %>
                </span>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </body>
</html>
