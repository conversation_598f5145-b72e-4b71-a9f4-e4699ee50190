<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <div class="py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold text-gray-900">Tenants</h1>
      <% if policy(Tenant).create? %>
        <%= link_to "New Tenant", new_tenant_path, class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" %>
      <% end %>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <ul class="divide-y divide-gray-200">
        <% @tenants.each do |tenant| %>
          <li>
            <div class="px-4 py-4 flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-10 w-10 rounded-full bg-blue-300 flex items-center justify-center">
                    <span class="text-sm font-medium text-blue-700">
                      <%= tenant.name.first.upcase %>
                    </span>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="flex items-center">
                    <p class="text-sm font-medium text-gray-900">
                      <%= tenant.name %>
                    </p>
                    <% unless tenant.active? %>
                      <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Inactive
                      </span>
                    <% end %>
                  </div>
                  <p class="text-sm text-gray-500"><%= tenant.subdomain %></p>
                  <% if tenant.description.present? %>
                    <p class="text-xs text-gray-400"><%= tenant.description %></p>
                  <% end %>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <% if policy(tenant).show? %>
                  <%= link_to "View", tenant_path(tenant), class: "text-blue-600 hover:text-blue-900 text-sm" %>
                <% end %>
                <% if policy(tenant).edit? %>
                  <%= link_to "Edit", edit_tenant_path(tenant), class: "text-green-600 hover:text-green-900 text-sm" %>
                <% end %>
                <% if policy(tenant).destroy? %>
                  <%= link_to "Delete", tenant_path(tenant), 
                      data: { "turbo-method": :delete, "turbo-confirm": "Are you sure?" }, 
                      class: "text-red-600 hover:text-red-900 text-sm" %>
                <% end %>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    </div>

    <% if @tenants.empty? %>
      <div class="text-center py-12">
        <p class="text-gray-500">No tenants found.</p>
        <% if policy(Tenant).create? %>
          <%= link_to "Create the first tenant", new_tenant_path, class: "mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded inline-block" %>
        <% end %>
      </div>
    <% end %>
  </div>
</div>
