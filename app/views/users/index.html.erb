<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <div class="py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold text-gray-900">Users</h1>
      <% if policy(User).create? %>
        <%= link_to "New User", new_user_path, class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" %>
      <% end %>
    </div>

    <% if current_tenant %>
      <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <p class="text-sm text-blue-800">
          <strong>Current Tenant:</strong> <%= current_tenant.name %> (<%= current_tenant.subdomain %>)
        </p>
      </div>
    <% else %>
      <div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p class="text-sm text-yellow-800">
          <strong>Super Admin Mode:</strong> No tenant context (can see all data)
        </p>
      </div>
    <% end %>

    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <ul class="divide-y divide-gray-200">
        <% @users.each do |user| %>
          <li>
            <div class="px-4 py-4 flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                    <span class="text-sm font-medium text-gray-700">
                      <%= user.display_name.first.upcase %>
                    </span>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="flex items-center">
                    <p class="text-sm font-medium text-gray-900">
                      <%= user.display_name %>
                    </p>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= user.role == 'admin' ? 'bg-red-100 text-red-800' : user.role == 'manager' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800' %>">
                      <%= user.role.capitalize %>
                    </span>
                    <% unless user.active? %>
                      <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Inactive
                      </span>
                    <% end %>
                  </div>
                  <p class="text-sm text-gray-500"><%= user.email %></p>
                  <% if user.tenant %>
                    <p class="text-xs text-gray-400">Tenant: <%= user.tenant.name %></p>
                  <% else %>
                    <p class="text-xs text-gray-400">Super Admin (No Tenant)</p>
                  <% end %>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <% if policy(user).show? %>
                  <%= link_to "View", user_path(user), class: "text-blue-600 hover:text-blue-900 text-sm" %>
                <% end %>
                <% if policy(user).edit? %>
                  <%= link_to "Edit", edit_user_path(user), class: "text-green-600 hover:text-green-900 text-sm" %>
                <% end %>
                <% if policy(user).destroy? %>
                  <%= link_to "Delete", user_path(user),
                      data: { "turbo-method": :delete, "turbo-confirm": "Are you sure?" },
                      class: "text-red-600 hover:text-red-900 text-sm" %>
                <% end %>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    </div>

    <% if @users.empty? %>
      <div class="text-center py-12">
        <p class="text-gray-500">No users found.</p>
        <% if policy(User).create? %>
          <%= link_to "Create the first user", new_user_path, class: "mt-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded inline-block" %>
        <% end %>
      </div>
    <% end %>
  </div>
</div>
