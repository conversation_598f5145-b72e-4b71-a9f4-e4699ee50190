# frozen_string_literal: true

module TenantAuthorization
  extend ActiveSupport::Concern

  included do
    # Ensure tenant is set for tenant-aware models
    before_action :ensure_tenant_context, except: [ :index, :new, :create ]
  end

  private

  def ensure_tenant_context
    return unless tenant_required_for_action?
    return if current_tenant.present?

    flash[:alert] = "Access denied: No tenant context available."
    redirect_to root_path
  end

  def tenant_required_for_action?
    # Override in controllers that need tenant context
    false
  end

  # Helper method to scope records to current tenant
  def tenant_scoped(model_class)
    if current_tenant
      model_class.where(tenant: current_tenant)
    else
      model_class.none
    end
  end

  # Helper method to build new records with current tenant
  def build_with_tenant(model_class, attributes = {})
    record = model_class.new(attributes)
    record.tenant = current_tenant if current_tenant && record.respond_to?(:tenant=)
    record
  end

  # Check if current user can access a specific tenant
  def can_access_tenant?(tenant)
    return false unless user_signed_in?
    return true if current_user.admin? # Super admins can access any tenant
    current_user.tenant_id == tenant.id
  end

  # Switch tenant context (for super admins)
  def switch_tenant_context(tenant)
    if current_user&.admin?
      ActsAsTenant.current_tenant = tenant
      session[:switched_tenant_id] = tenant.id
      true
    else
      false
    end
  end

  # Restore original tenant context
  def restore_tenant_context
    if current_user&.tenant
      ActsAsTenant.current_tenant = current_user.tenant
      session.delete(:switched_tenant_id)
    end
  end
end
