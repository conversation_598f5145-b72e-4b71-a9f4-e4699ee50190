class ApplicationController < ActionController::Base
  include Pundit::Authorization

  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern

  # Ensure user is authenticated before any action
  before_action :authenticate_user!

  # Set current tenant based on authenticated user
  before_action :set_current_tenant

  # Pundit authorization error handling
  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized
  rescue_from ActsAsTenant::Errors::NoTenantSet, with: :tenant_not_set

  # Handle GET requests to sign out (for browser cache/bookmark issues)
  def sign_out_get
    if user_signed_in?
      sign_out(current_user)
      flash[:notice] = "You have been signed out successfully."
    end
    redirect_to new_user_session_path
  end

  private

  def set_current_tenant
    if user_signed_in? && current_user.tenant.present?
      ActsAsTenant.current_tenant = current_user.tenant
    elsif user_signed_in?
      # Handle users without tenants (e.g., super admins or during migration)
      ActsAsTenant.current_tenant = nil
    end
  end

  def user_not_authorized(exception)
    policy_name = exception.policy.class.to_s.underscore
    flash[:alert] = t "#{policy_name}.#{exception.query}", scope: "pundit", default: :default
    redirect_back(fallback_location: root_path)
  end

  def tenant_not_set
    flash[:alert] = "Access denied: No tenant context available."
    redirect_to root_path
  end

  # Helper method to check if current user belongs to a specific tenant
  def current_tenant
    ActsAsTenant.current_tenant
  end
  helper_method :current_tenant

  # Helper method to check if user has access to tenant
  def tenant_accessible?(tenant)
    return false unless user_signed_in?
    return true if current_user.admin? # Admins can access any tenant
    current_user.tenant_id == tenant.id
  end
  helper_method :tenant_accessible?
end
