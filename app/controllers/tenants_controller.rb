# frozen_string_literal: true

class TenantsController < ApplicationController
  before_action :set_tenant, only: [ :show, :edit, :update, :destroy, :activate, :deactivate, :switch_to ]

  def index
    @tenants = policy_scope(Tenant)
    authorize Tenant
  end

  def show
    authorize @tenant
  end

  def new
    @tenant = Tenant.new
    authorize @tenant
  end

  def create
    @tenant = Tenant.new(tenant_params)
    authorize @tenant

    if @tenant.save
      redirect_to @tenant, notice: "Tenant was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    authorize @tenant
  end

  def update
    authorize @tenant

    if @tenant.update(tenant_params)
      redirect_to @tenant, notice: "Tenant was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    authorize @tenant
    @tenant.destroy
    redirect_to tenants_url, notice: "Tenant was successfully deleted."
  end

  def activate
    authorize @tenant, :activate?
    @tenant.activate!
    redirect_to @tenant, notice: "Tenant was successfully activated."
  end

  def deactivate
    authorize @tenant, :deactivate?
    @tenant.deactivate!
    redirect_to @tenant, notice: "Tenant was successfully deactivated."
  end

  def switch_to
    authorize @tenant, :switch_to?

    if tenant_accessible?(@tenant)
      # For demo purposes - in production you might want more sophisticated tenant switching
      session[:switched_tenant_id] = @tenant.id
      redirect_to root_path, notice: "Switched to tenant: #{@tenant.name}"
    else
      redirect_to tenants_path, alert: "You do not have access to this tenant."
    end
  end

  private

  def set_tenant
    @tenant = Tenant.find(params[:id])
  end

  def tenant_params
    params.require(:tenant).permit(:name, :subdomain, :description, :contact_email, :contact_phone, :active)
  end
end
