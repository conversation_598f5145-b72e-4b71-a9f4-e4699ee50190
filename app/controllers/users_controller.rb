# frozen_string_literal: true

class UsersController < ApplicationController
  before_action :set_user, only: [:show, :edit, :update, :destroy, :activate, :deactivate]

  def index
    @users = policy_scope(User)
    authorize User
  end

  def show
    authorize @user
  end

  def new
    @user = User.new
    authorize @user
  end

  def create
    @user = User.new(user_params)
    @user.tenant = current_tenant if current_tenant
    authorize @user

    if @user.save
      redirect_to @user, notice: 'User was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    authorize @user
  end

  def update
    authorize @user

    if @user.update(user_params)
      redirect_to @user, notice: 'User was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    authorize @user
    @user.destroy
    redirect_to users_url, notice: 'User was successfully deleted.'
  end

  def activate
    authorize @user, :activate?
    @user.activate!
    redirect_to @user, notice: 'User was successfully activated.'
  end

  def deactivate
    authorize @user, :deactivate?
    @user.deactivate!
    redirect_to @user, notice: 'User was successfully deactivated.'
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def user_params
    params.require(:user).permit(:email, :name, :first_name, :last_name, :role, :active)
  end
end
