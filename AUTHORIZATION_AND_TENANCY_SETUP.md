# Authorization and Multi-Tenancy Setup

This document outlines the comprehensive setup of Pundit for authorization and Acts As Tenant for multi-tenancy in your Rails application.

## 🎯 Overview

We have successfully implemented:
- **Pundit** for role-based authorization with tenant-aware policies
- **Acts As Tenant** for multi-tenancy with proper data isolation
- **Integration** between both systems for secure, tenant-aware authorization
- **Ahoy** analytics with tenant-aware tracking

## 🏗️ Architecture

### Multi-Tenancy Structure
- **Tenant Model**: Central tenant management with subdomains and activation status
- **User Model**: Belongs to tenants with role-based permissions (admin, manager, user)
- **Tenant Isolation**: Automatic scoping of data to current tenant context
- **Super Admin Support**: Users without tenants can access all data

### Authorization Structure
- **ApplicationPolicy**: Base policy with tenant-aware authorization logic
- **Role-Based Access**: Admin, manager, and user roles with different permissions
- **Tenant Boundaries**: Policies automatically respect tenant isolation
- **Error Handling**: Comprehensive error handling for authorization failures

## 📁 Key Files Created/Modified

### Models
- `app/models/tenant.rb` - Tenant model with validations and scopes
- `app/models/user.rb` - Enhanced with tenant association and role management
- `app/models/ahoy/visit.rb` - Tenant-aware analytics visits
- `app/models/ahoy/event.rb` - Tenant-aware analytics events

### Policies
- `app/policies/application_policy.rb` - Base policy with tenant-aware logic
- `app/policies/user_policy.rb` - User-specific authorization rules
- `app/policies/tenant_policy.rb` - Tenant management authorization

### Controllers
- `app/controllers/application_controller.rb` - Pundit integration and tenant switching
- `app/controllers/users_controller.rb` - User management with authorization
- `app/controllers/tenants_controller.rb` - Tenant management with authorization
- `app/controllers/concerns/tenant_authorization.rb` - Reusable tenant authorization logic

### Configuration
- `config/initializers/acts_as_tenant.rb` - Acts As Tenant configuration
- `config/initializers/ahoy.rb` - Tenant-aware analytics configuration
- `config/locales/pundit.en.yml` - Authorization error messages

### Database
- `db/migrate/*_create_tenants.rb` - Tenant table with proper constraints
- `db/migrate/*_add_tenant_to_users.rb` - User-tenant association
- `db/migrate/*_add_tenant_to_ahoy_tables.rb` - Tenant-aware analytics

## 🔐 Security Features

### Tenant Isolation
- Automatic data scoping to current tenant
- Prevention of cross-tenant data access
- Secure tenant switching for super admins

### Authorization Layers
- Controller-level authorization with Pundit
- Policy-based permissions with role checking
- Tenant boundary enforcement in all policies

### Error Handling
- Graceful handling of authorization failures
- Proper error messages for different scenarios
- Secure fallback behaviors

## 👥 User Roles and Permissions

### Super Admin (No Tenant)
- Can access all tenants and data
- Can create, update, and delete tenants
- Can manage users across all tenants
- Can switch between tenant contexts

### Tenant Admin
- Full access within their tenant
- Can manage users in their tenant
- Can update tenant settings
- Cannot access other tenants

### Manager
- Limited administrative access within tenant
- Can view and update users in their tenant
- Cannot delete users or change roles

### Regular User
- Can only view and update their own profile
- Cannot access other users' data
- Cannot perform administrative actions

## 🧪 Sample Data

The application includes comprehensive seed data:

### Tenants
- **Acme Corporation** (acme-corp) - Active
- **Beta Inc** (beta-inc) - Active  
- **Gamma LLC** (gamma-llc) - Inactive

### Users
- **Super Admin**: <EMAIL> / password123
- **Acme Admin**: <EMAIL> / password123
- **Acme Manager**: <EMAIL> / password123
- **Acme User**: <EMAIL> / password123
- **Beta Admin**: <EMAIL> / password123
- **Beta User**: <EMAIL> / password123

## 🚀 Testing the Setup

1. **Start the server**: `rails server -p 3001`
2. **Visit**: http://localhost:3001
3. **Sign in** with any of the sample credentials
4. **Test authorization** by trying different actions with different users
5. **Verify tenant isolation** by switching between users from different tenants

## 🔧 Key Features Implemented

### Pundit Integration
- ✅ Application-wide authorization with Pundit
- ✅ Tenant-aware policy base class
- ✅ Role-based permissions
- ✅ Comprehensive error handling
- ✅ Localized error messages

### Acts As Tenant Integration  
- ✅ Automatic tenant scoping
- ✅ Tenant switching for super admins
- ✅ Optional tenant support for super admins
- ✅ Secure tenant isolation

### Combined Features
- ✅ Policies respect tenant boundaries
- ✅ Authorization works within tenant context
- ✅ Super admin can access all tenants
- ✅ Regular users are isolated to their tenant
- ✅ Analytics tracking is tenant-aware

## 📈 Next Steps

Consider implementing:
- Subdomain-based tenant routing
- API authentication with tenant scoping
- Advanced role management
- Tenant-specific customizations
- Billing and subscription management
- Advanced analytics and reporting

## 🛡️ Production Considerations

- Review and adjust role permissions based on business requirements
- Implement proper logging for authorization events
- Add rate limiting and security monitoring
- Consider implementing API rate limiting per tenant
- Set up proper backup and disaster recovery procedures
- Implement tenant data export/import functionality
