Rails.application.routes.draw do
  devise_for :users

  # Temporary redirect for sign out GET requests (for browser cache issues)
  get '/users/sign_out', to: redirect { |params, request|
    "/users/sign_in"
  }

  # Admin and management routes
  resources :users, constraints: { id: /\d+/ } do
    member do
      patch :activate
      patch :deactivate
    end
  end

  resources :tenants do
    member do
      patch :activate
      patch :deactivate
      post :switch_to
    end
  end

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Defines the root path route ("/")
  root "users#index"
end
