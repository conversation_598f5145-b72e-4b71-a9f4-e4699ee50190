class Ahoy::Store < Ahoy::DatabaseStore
  def track_visit(data)
    data[:tenant_id] = ActsAsTenant.current_tenant&.id if ActsAsTenant.current_tenant
    super(data)
  end

  def track_event(data)
    data[:tenant_id] = ActsAsTenant.current_tenant&.id if ActsAsTenant.current_tenant
    super(data)
  end
end

# set to true for JavaScript tracking
Ahoy.api = false

# set to true for geocoding (and add the geocoder gem to your Gemfile)
# we recommend configuring local geocoding as well
# see https://github.com/ankane/ahoy#geocoding
Ahoy.geocode = false
