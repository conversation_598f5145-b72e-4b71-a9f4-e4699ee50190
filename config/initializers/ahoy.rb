class Ahoy::Store < Ahoy::DatabaseStore
  def visit_model
    Ahoy::Visit
  end

  def event_model
    Ahoy::Event
  end

  def visit_properties(data, request)
    properties = super
    properties[:tenant_id] = ActsAsTenant.current_tenant&.id if ActsAsTenant.current_tenant
    properties
  end

  def event_properties(data, request)
    properties = super
    properties[:tenant_id] = ActsAsTenant.current_tenant&.id if ActsAsTenant.current_tenant
    properties
  end
end

# set to true for JavaScript tracking
Ahoy.api = false

# set to true for geocoding (and add the geocoder gem to your Gemfile)
# we recommend configuring local geocoding as well
# see https://github.com/ankane/ahoy#geocoding
Ahoy.geocode = false
