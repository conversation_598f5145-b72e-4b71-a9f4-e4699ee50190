# frozen_string_literal: true

require "test_helper"
require "securerandom"

class AuthorizationAndTenancyTest < ActionDispatch::IntegrationTest
  # Disable fixtures to avoid conflicts with our test data
  self.use_transactional_tests = true

  def setup
    # Create test tenants with unique subdomains
    @tenant1 = Tenant.create!(
      name: "Test Tenant 1",
      subdomain: "test1-#{SecureRandom.hex(4)}",
      active: true
    )

    @tenant2 = Tenant.create!(
      name: "Test Tenant 2",
      subdomain: "test2-#{SecureRandom.hex(4)}",
      active: true
    )

    # Create test users with unique emails
    @super_admin = User.create!(
      email: "superadmin-#{SecureRandom.hex(4)}@test.com",
      password: "password123",
      name: "Super Admin",
      role: "admin",
      active: true,
      tenant: nil
    )

    ActsAsTenant.with_tenant(@tenant1) do
      @tenant1_admin = User.create!(
        email: "admin1-#{SecureRandom.hex(4)}@test.com",
        password: "password123",
        name: "Tenant 1 Admin",
        role: "admin",
        active: true,
        tenant: @tenant1
      )

      @tenant1_user = User.create!(
        email: "user1-#{SecureRandom.hex(4)}@test.com",
        password: "password123",
        name: "Tenant 1 User",
        role: "user",
        active: true,
        tenant: @tenant1
      )
    end

    ActsAsTenant.with_tenant(@tenant2) do
      @tenant2_user = User.create!(
        email: "user2-#{SecureRandom.hex(4)}@test.com",
        password: "password123",
        name: "Tenant 2 User",
        role: "user",
        active: true,
        tenant: @tenant2
      )
    end
  end

  def teardown
    # Clean up test data
    User.delete_all
    Tenant.delete_all
    ActsAsTenant.current_tenant = nil
  end

  test "super admin can access all tenants and users" do
    sign_in @super_admin

    get users_path
    assert_response :success, "Super admin should be able to access users index"

    get tenants_path
    assert_response :success, "Super admin should be able to access tenants index"
  end

  test "tenant admin can access their tenant users but not other tenants" do
    sign_in @tenant1_admin

    get users_path
    assert_response :success

    # Should be able to see tenant 1 user
    get user_path(@tenant1_user)
    assert_response :success
  end

  test "regular user can only access their own profile" do
    sign_in @tenant1_user

    get user_path(@tenant1_user)
    assert_response :success
  end

  test "tenant isolation is enforced" do
    sign_in @tenant1_user

    # Should not be able to access tenant 2 user
    # This should either raise RecordNotFound or redirect due to authorization
    get user_path(@tenant2_user)

    # Check that we either got a 404 or were redirected due to authorization
    assert_includes [302, 404], response.status, "Expected redirect or not found, got #{response.status}"

    if response.status == 302
      assert_match(/not authorized|access denied/i, flash[:alert] || "")
    end
  end

  test "pundit authorization works with tenant boundaries" do
    sign_in @tenant1_user

    # Regular user should not be able to create other users
    get new_user_path
    assert_redirected_to root_path
    assert_match(/not authorized/, flash[:alert])
  end

  private

  def sign_in(user)
    post user_session_path, params: {
      user: {
        email: user.email,
        password: "password123"
      }
    }, headers: { "Accept" => "text/html" }

    # Follow redirect after successful sign in
    follow_redirect! if response.redirect?
  end
end
