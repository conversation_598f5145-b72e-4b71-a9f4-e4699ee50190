# frozen_string_literal: true

require "test_helper"

class AuthorizationAndTenancyTest < ActionDispatch::IntegrationTest
  def setup
    # Create test tenants
    @tenant1 = Tenant.create!(
      name: "Test Tenant 1",
      subdomain: "test1",
      active: true
    )

    @tenant2 = Tenant.create!(
      name: "Test Tenant 2",
      subdomain: "test2",
      active: true
    )

    # Create test users
    @super_admin = User.create!(
      email: "<EMAIL>",
      password: "password123",
      name: "Super Admin",
      role: "admin",
      active: true,
      tenant: nil
    )

    ActsAsTenant.with_tenant(@tenant1) do
      @tenant1_admin = User.create!(
        email: "<EMAIL>",
        password: "password123",
        name: "Tenant 1 Admin",
        role: "admin",
        active: true,
        tenant: @tenant1
      )

      @tenant1_user = User.create!(
        email: "<EMAIL>",
        password: "password123",
        name: "Tenant 1 User",
        role: "user",
        active: true,
        tenant: @tenant1
      )
    end

    ActsAsTenant.with_tenant(@tenant2) do
      @tenant2_user = User.create!(
        email: "<EMAIL>",
        password: "password123",
        name: "Tenant 2 User",
        role: "user",
        active: true,
        tenant: @tenant2
      )
    end
  end

  test "super admin can access all tenants and users" do
    sign_in @super_admin

    get users_path
    assert_response :success

    get tenants_path
    assert_response :success
  end

  test "tenant admin can access their tenant users but not other tenants" do
    sign_in @tenant1_admin

    get users_path
    assert_response :success

    # Should be able to see tenant 1 user
    get user_path(@tenant1_user)
    assert_response :success
  end

  test "regular user can only access their own profile" do
    sign_in @tenant1_user

    get user_path(@tenant1_user)
    assert_response :success
  end

  test "tenant isolation is enforced" do
    sign_in @tenant1_user

    # Should not be able to access tenant 2 user
    assert_raises(ActiveRecord::RecordNotFound) do
      get user_path(@tenant2_user)
    end
  end

  test "pundit authorization works with tenant boundaries" do
    sign_in @tenant1_user

    # Regular user should not be able to create other users
    get new_user_path
    assert_redirected_to root_path
    assert_match(/not authorized/, flash[:alert])
  end

  private

  def sign_in(user)
    post user_session_path, params: {
      user: {
        email: user.email,
        password: "password123"
      }
    }
  end
end
