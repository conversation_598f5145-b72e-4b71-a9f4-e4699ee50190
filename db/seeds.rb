# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

# frozen_string_literal: true

# Create sample tenants
puts "Creating tenants..."

tenant1 = Tenant.find_or_create_by(subdomain: 'acme-corp') do |t|
  t.name = 'Acme Corporation'
  t.description = 'A leading technology company'
  t.contact_email = '<EMAIL>'
  t.contact_phone = '******-0123'
  t.active = true
end

tenant2 = Tenant.find_or_create_by(subdomain: 'beta-inc') do |t|
  t.name = 'Beta Inc'
  t.description = 'Innovative solutions provider'
  t.contact_email = '<EMAIL>'
  t.contact_phone = '******-0456'
  t.active = true
end

tenant3 = Tenant.find_or_create_by(subdomain: 'gamma-llc') do |t|
  t.name = 'Gamma LLC'
  t.description = 'Professional services firm'
  t.contact_email = '<EMAIL>'
  t.contact_phone = '******-0789'
  t.active = false
end

puts "Created #{Tenant.count} tenants"

# Create sample users for each tenant
puts "Creating users..."

# Super admin (no tenant)
super_admin = User.find_or_create_by(email: '<EMAIL>') do |u|
  u.password = 'password123'
  u.password_confirmation = 'password123'
  u.name = 'Super Admin'
  u.first_name = 'Super'
  u.last_name = 'Admin'
  u.role = 'admin'
  u.active = true
  u.tenant = nil
end

# Acme Corp users
ActsAsTenant.with_tenant(tenant1) do
  acme_admin = User.find_or_create_by(email: '<EMAIL>') do |u|
    u.password = 'password123'
    u.password_confirmation = 'password123'
    u.name = 'Acme Admin'
    u.first_name = 'John'
    u.last_name = 'Smith'
    u.role = 'admin'
    u.active = true
    u.tenant = tenant1
  end

  acme_manager = User.find_or_create_by(email: '<EMAIL>') do |u|
    u.password = 'password123'
    u.password_confirmation = 'password123'
    u.name = 'Acme Manager'
    u.first_name = 'Jane'
    u.last_name = 'Doe'
    u.role = 'manager'
    u.active = true
    u.tenant = tenant1
  end

  acme_user = User.find_or_create_by(email: '<EMAIL>') do |u|
    u.password = 'password123'
    u.password_confirmation = 'password123'
    u.name = 'Acme User'
    u.first_name = 'Bob'
    u.last_name = 'Johnson'
    u.role = 'user'
    u.active = true
    u.tenant = tenant1
  end
end

# Beta Inc users
ActsAsTenant.with_tenant(tenant2) do
  beta_admin = User.find_or_create_by(email: '<EMAIL>') do |u|
    u.password = 'password123'
    u.password_confirmation = 'password123'
    u.name = 'Beta Admin'
    u.first_name = 'Alice'
    u.last_name = 'Wilson'
    u.role = 'admin'
    u.active = true
    u.tenant = tenant2
  end

  beta_user = User.find_or_create_by(email: '<EMAIL>') do |u|
    u.password = 'password123'
    u.password_confirmation = 'password123'
    u.name = 'Beta User'
    u.first_name = 'Charlie'
    u.last_name = 'Brown'
    u.role = 'user'
    u.active = true
    u.tenant = tenant2
  end
end

# Gamma LLC users (inactive tenant)
ActsAsTenant.with_tenant(tenant3) do
  gamma_admin = User.find_or_create_by(email: '<EMAIL>') do |u|
    u.password = 'password123'
    u.password_confirmation = 'password123'
    u.name = 'Gamma Admin'
    u.first_name = 'David'
    u.last_name = 'Lee'
    u.role = 'admin'
    u.active = false
    u.tenant = tenant3
  end
end

puts "Created #{User.count} users"

puts "Seed data creation completed!"
puts ""
puts "Sample login credentials:"
puts "Super Admin: <EMAIL> / password123"
puts "Acme Admin: <EMAIL> / password123"
puts "Acme Manager: <EMAIL> / password123"
puts "Acme User: <EMAIL> / password123"
puts "Beta Admin: <EMAIL> / password123"
puts "Beta User: <EMAIL> / password123"
